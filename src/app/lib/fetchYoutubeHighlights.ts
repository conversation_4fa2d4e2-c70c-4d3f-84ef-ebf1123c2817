import {cleanYoutubeId, ExtractTeamsFromTitle, generateLabel} from '@/components/Shared/Util'
import {Highlight} from '@/types/highlights'
import {XMLParser} from 'fast-xml-parser'

export type LeagueConfig = {
    channelID: string
    startDate: Date
    keywords: {
        included: string[]
        excluded: string[]
    }
    league: string
    country: string
    season: string
}

export async function fetchHighlightsFromYoutubeAPI(config: LeagueConfig): Promise<Highlight[]> {
    const apiKey = process.env.YOUTUBE_API_KEY
    const {channelID, startDate, keywords, league, country, season} = config

    const playlistResponse = await fetch(
        `https://www.googleapis.com/youtube/v3/channels?part=contentDetails&fields=items(contentDetails/relatedPlaylists/uploads)&id=${channelID}&key=${apiKey}&hl=en`
    )
    const playlistData = await playlistResponse.json()

    if (!playlistData.items || playlistData.items.length === 0) {
        throw new Error('Could not retrieve upload playlist')
    }

    const uploadsPlaylistId = playlistData.items[0].contentDetails.relatedPlaylists.uploads
    let nextPageToken = ''
    const videos: Highlight[] = []
    let shouldStop = false

    do {
        const videosResponse = await fetch(
            `https://www.googleapis.com/youtube/v3/playlistItems?part=snippet&fields=items(snippet(publishedAt,title,resourceId/videoId)),nextPageToken&maxResults=50&playlistId=${uploadsPlaylistId}&pageToken=${nextPageToken}&key=${apiKey}&hl=en`
        )
        const videosData = await videosResponse.json()

        if (!videosData.items) break

        for (const item of videosData.items) {
            const publishedAt = new Date(item.snippet.publishedAt)
            if (publishedAt < startDate) {
                shouldStop = true
                break
            }

            // For Bundesliga, remove prefix before the first team name
            let processedTitle = item.snippet.title
            if (league.toLowerCase() === 'bundesliga') {
                // Look for pattern: "Prefix | TEAM1 - TEAM2 | ..."
                // Try different pipe patterns: " | ", " |", "| ", "|"
                const pipePatterns = [' | ', ' |', '| ', '|']
                let pipeIndex = -1
                let pipeLength = 0

                for (const pattern of pipePatterns) {
                    const index = processedTitle.indexOf(pattern)
                    if (index !== -1) {
                        pipeIndex = index
                        pipeLength = pattern.length
                        break
                    }
                }

                if (pipeIndex !== -1) {
                    const beforePipe = processedTitle.substring(0, pipeIndex)
                    const teamSeparators = [' - ', ' vs. ', ' vs ', ' v. ', ' v ']
                    const hasTeamSeparator = teamSeparators.some((sep) => beforePipe.includes(sep))

                    // Only remove prefix if it doesn't contain team names (no separators)
                    if (!hasTeamSeparator) {
                        processedTitle = processedTitle.substring(pipeIndex + pipeLength)
                        console.log('Bundesliga prefix removed:', {
                            original: item.snippet.title,
                            processed: processedTitle,
                        })
                    }
                }
            }

            const teams = ExtractTeamsFromTitle(processedTitle)
            if (league.toLowerCase() === 'bundesliga' && teams.length === 2) {
                console.log('Bundesliga teams extracted:', {
                    title: processedTitle,
                    teams: teams,
                })
            }

            const normalizedTitle = item.snippet.title
                .normalize('NFD')
                .replace(/[\u0300-\u036f]/g, '')
                .toLowerCase()

            const highlightKeywords = ['highlights', 'extended highlights', 'match highlights']

            const highlightPresent = highlightKeywords.some((kw) => normalizedTitle.includes(kw))
            const allIncludedPresent = keywords.included.every((k) =>
                normalizedTitle.includes(k.toLowerCase())
            )
            const anyExcludedPresent = keywords.excluded.some((k) =>
                normalizedTitle.includes(k.toLowerCase())
            )

            if (
                teams.length === 2 &&
                allIncludedPresent &&
                highlightPresent &&
                !anyExcludedPresent
            ) {
                const baseLabel = generateLabel(`${teams[0]}-${teams[1]}`)
                const uniqueLabel = `${baseLabel}-${cleanYoutubeId(item.snippet.resourceId.videoId).substring(0, 6)}`
                videos.push({
                    id: '',
                    name: `${teams[0]} vs. ${teams[1]}`,
                    label: uniqueLabel,
                    duration: '',
                    thumbnail: `https://img.youtube.com/vi/${item.snippet.resourceId.videoId}/maxresdefault.jpg`,
                    video_url: `https://www.youtube.com/watch?v=${item.snippet.resourceId.videoId}`,
                    sport: 'soccer',
                    season,
                    created_at: item.snippet.publishedAt,
                    league,
                    country,
                })
            }
        }

        if (shouldStop) break

        nextPageToken = videosData.nextPageToken || ''
    } while (nextPageToken)

    return videos
}

export async function fetchRSSFeed(channelId: string): Promise<any> {
    const response = await fetch(`https://www.youtube.com/feeds/videos.xml?channel_id=${channelId}`)
    const xmlData = await response.text()

    const parser = new XMLParser({
        ignoreAttributes: false,
        attributeNamePrefix: '@_',
    })

    return parser.parse(xmlData)
}

export async function fetchHighlightsFromRSSFeed(config: LeagueConfig): Promise<Highlight[]> {
    const {channelID, startDate, keywords, league, country, season} = config

    const feed = await fetchRSSFeed(channelID)
    const entries = feed.feed.entry || []
    const videos: Highlight[] = []

    for (const entry of entries) {
        const publishedAt = new Date(entry.published)
        if (publishedAt < startDate) continue

        const title = entry.title
        const videoId = entry['yt:videoId']

        // For Bundesliga, remove prefix before the first team name
        let processedTitle = title
        if (league.toLowerCase() === 'bundesliga') {
            // Look for pattern: "Prefix | TEAM1 - TEAM2 | ..."
            // Try different pipe patterns: " | ", " |", "| ", "|"
            const pipePatterns = [' | ', ' |', '| ', '|']
            let pipeIndex = -1
            let pipeLength = 0

            for (const pattern of pipePatterns) {
                const index = processedTitle.indexOf(pattern)
                if (index !== -1) {
                    pipeIndex = index
                    pipeLength = pattern.length
                    break
                }
            }

            if (pipeIndex !== -1) {
                const beforePipe = processedTitle.substring(0, pipeIndex)
                const teamSeparators = [' - ', ' vs. ', ' vs ', ' v. ', ' v ']
                const hasTeamSeparator = teamSeparators.some((sep) => beforePipe.includes(sep))

                // Only remove prefix if it doesn't contain team names (no separators)
                if (!hasTeamSeparator) {
                    processedTitle = processedTitle.substring(pipeIndex + pipeLength)
                }
            }
        }

        const teams = ExtractTeamsFromTitle(processedTitle)

        const normalizedTitle = title
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .toLowerCase()

        const highlightKeywords = ['highlights', 'extended highlights', 'match highlights']
        const highlightPresent = highlightKeywords.some((kw) => normalizedTitle.includes(kw))
        const allIncludedPresent = keywords.included.every((k) =>
            normalizedTitle.includes(k.toLowerCase())
        )
        const anyExcludedPresent = keywords.excluded.some((k) =>
            normalizedTitle.includes(k.toLowerCase())
        )

        if (teams.length === 2 && allIncludedPresent && highlightPresent && !anyExcludedPresent) {
            const baseLabel = generateLabel(`${teams[0]}-${teams[1]}`)
            const uniqueLabel = `${baseLabel}-${cleanYoutubeId(videoId).substring(0, 6)}`

            videos.push({
                id: '',
                name: `${teams[0]} vs. ${teams[1]}`,
                label: uniqueLabel,
                duration: '',
                thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
                video_url: `https://www.youtube.com/watch?v=${videoId}`,
                sport: 'soccer',
                season,
                created_at: entry.published,
                league,
                country,
            })
        }
    }

    return videos
}
