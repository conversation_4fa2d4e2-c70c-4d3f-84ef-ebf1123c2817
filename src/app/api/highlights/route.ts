import {
    fetchHighlightsFromRSSFeed,
    fetchHighlightsFromYoutubeAPI,
    LeagueConfig,
} from '@/app/lib/fetchYoutubeHighlights'
import {Highlight} from '@/types/highlights'
import {HighlightsCache} from '@/utils/cache'
import {supabase} from '@/utils/supabaseClient'
import {NextRequest, NextResponse} from 'next/server'

const CRON_LAST_RUN_DATE = new Date(Date.now() - 5 * 60 * 1000)

// Define league configurations
const leagueConfigs: Record<string, LeagueConfig> = {
    'premier-league': {
        channelID: 'UCqZQlzSHbVJrwrn5XvzrzcA',
        startDate: CRON_LAST_RUN_DATE,
        keywords: {
            included: ['v.', 'PREMIER LEAGUE HIGHLIGHTS'],
            excluded: [],
        },
        league: 'premier-league',
        country: 'usa',
        season: '2025/26',
    },
    laliga: {
        channelID: 'XvfzLX3i4IGWAm4sbmA',
        startDate: new Date('2024-08-15T00:00:00Z'),
        keywords: {
            included: ['RESUMEN', '-'],
            excluded: ['20', '/'],
        },
        league: 'laliga',
        country: 'usa',
        season: '2024/25',
    },
    bundesliga: {
        channelID: 'UC6UL29enLNe4mqwTfAyeNuw',
        startDate: new Date('2024-08-20T00:00:00Z'),
        keywords: {
            included: ['Highlights', '-', 'Bundesliga'],
            excluded: [],
        },
        league: 'bundesliga',
        country: 'usa',
        season: '2024/25',
    },
    'serie-a': {
        channelID: 'UCET00YnetHT7tOpu12v8jxg',
        startDate: CRON_LAST_RUN_DATE,
        keywords: {
            included: ['vs.', 'Serie A'],
            excluded: [],
        },
        league: 'serie-a',
        country: 'usa',
        season: '2025/26',
    },
    'ligue-1': {
        channelID: 'UC0YatYmg5JRYzXJPxIdRd8g',
        startDate: new Date('2024-08-12T00:00:00Z'),
        keywords: {
            included: ['vs', 'Ligue 1'],
            excluded: [],
        },
        league: 'ligue-1',
        country: 'usa',
        season: '2024/25',
    },
    mls: {
        channelID: 'UCSZbXT5TLLW_i-5W8FZpFsg',
        startDate: CRON_LAST_RUN_DATE,
        keywords: {
            included: ['vs.', 'Highlights'],
            excluded: ['Generation', 'U18', 'U16', 'adidas'],
        },
        league: 'mls',
        country: 'usa',
        season: '2025',
    },
    'champions-league': {
        channelID: 'UCET00YnetHT7tOpu12v8jxg',
        startDate: CRON_LAST_RUN_DATE,
        keywords: {
            included: ['UCL', 'Highlights'],
            excluded: ['/'],
        },
        league: 'champions-league',
        country: 'usa',
        season: '2025/26',
    },
    'europa-league': {
        channelID: 'UCf8YPuOWXlpTS7RibaJlP4g',
        startDate: CRON_LAST_RUN_DATE,
        keywords: {
            included: ['UEL'],
            excluded: [],
        },
        league: 'europa-league',
        country: 'usa',
        season: '2025/26',
    },
    'conference-league': {
        channelID: 'UCf8YPuOWXlpTS7RibaJlP4g',
        startDate: CRON_LAST_RUN_DATE,
        keywords: {
            included: ['UECL'],
            excluded: [],
        },
        league: 'conference-league',
        country: 'usa',
        season: '2025/26',
    },
    'club-world-cup': {
        channelID: 'UCSZ21xyG8w_33KriMM69IxQ',
        startDate: CRON_LAST_RUN_DATE,
        keywords: {
            included: ['Vs.', 'Club World Cup', 'Extended Highlights'],
            excluded: [],
        },
        league: 'club-world-cup',
        country: 'usa',
        season: '2025',
    },
}

// Type guard to check if a string is a valid league key
function isValidLeague(league: string): league is string {
    return league in leagueConfigs
}

// Function to save new highlights to Supabase
async function saveHighlightsToDatabase(
    highlights: Highlight[]
): Promise<{newCount: number; errors: any[]}> {
    const errors: any[] = []
    let newCount = 0

    for (const highlight of highlights) {
        try {
            // Check if highlight already exists by label (unique identifier)
            const {data: existing, error: checkError} = await supabase
                .from('highlights')
                .select('id')
                .eq('label', highlight.label)
                .single()

            if (checkError && checkError.code !== 'PGRST116') {
                // PGRST116 is "not found" error, which is expected for new highlights
                errors.push({highlight: highlight.label, error: checkError})
                continue
            }

            // If highlight doesn't exist, insert it
            if (!existing) {
                const {error: insertError} = await supabase.from('highlights').insert({
                    name: highlight.name,
                    label: highlight.label,
                    duration: highlight.duration,
                    thumbnail: highlight.thumbnail,
                    video_url: highlight.video_url,
                    sport: highlight.sport,
                    season: highlight.season,
                    created_at: highlight.created_at,
                    league: highlight.league,
                    country: highlight.country,
                })

                if (insertError) {
                    errors.push({highlight: highlight.label, error: insertError})
                } else {
                    newCount++
                }
            }
        } catch (error) {
            errors.push({highlight: highlight.label, error})
        }
    }

    return {newCount, errors}
}

export async function GET(request: NextRequest) {
    // Get the league from query parameters
    const from = request.nextUrl.searchParams.get('from')
    const league = request.nextUrl.searchParams.get('league')

    if (!league) {
        return NextResponse.json({error: 'Missing league parameter'}, {status: 400})
    }

    // Check if the league exists in our configurations
    if (!isValidLeague(league)) {
        return NextResponse.json({error: `Unsupported league: ${league}`}, {status: 400})
    }

    try {
        if (from === 'api') {
            const highlights = await fetchHighlightsFromYoutubeAPI(leagueConfigs[league])
            return NextResponse.json(highlights, {status: 200})
        } else {
            const highlights = await fetchHighlightsFromRSSFeed(leagueConfigs[league])

            // Save new highlights to database
            const {newCount, errors} = await saveHighlightsToDatabase(highlights)

            // Invalidate cache if new highlights were added
            if (newCount > 0) {
                console.log(`Invalidating cache for league: ${league}`)
                await HighlightsCache.invalidateForLeague(league)
            }

            // Log results
            console.log(
                `${league}: Found ${highlights.length} highlights, saved ${newCount} new ones`
            )
            if (errors.length > 0) {
                console.error(`${league}: Errors saving highlights:`, errors)
            }

            return NextResponse.json(
                {
                    highlights,
                    summary: {
                        total: highlights.length,
                        newSaved: newCount,
                        errors: errors.length,
                    },
                },
                {status: 200}
            )
        }
    } catch (error) {
        console.error(`Error fetching ${league} highlights:`, error)
        return NextResponse.json({error: `Error fetching ${league} highlights`}, {status: 500})
    }
}
